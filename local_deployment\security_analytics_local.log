2025-06-10 09:52:39,865 - control_panel - INFO - Using LOCAL backend for processing
2025-06-10 09:52:39,883 - control_panel - INFO - Project root: C:\Users\<USER>\Documents\hg-hackathon25-track3
2025-06-10 09:52:39,890 - control_panel - INFO - Looking for dataset at: C:\Users\<USER>\Documents\hg-hackathon25-track3\data\sample_inputs\5g_nidd_dataset.csv
2025-06-10 09:52:39,899 - control_panel - INFO - Looking for models at: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models
2025-06-10 09:52:39,899 - src.core.data_loader - INFO - Loading dataset from: C:\Users\<USER>\Documents\hg-hackathon25-track3\data\sample_inputs\5g_nidd_dataset.csv
2025-06-10 09:52:41,557 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-10 09:52:41,557 - control_panel - INFO - Data loader initialized successfully
2025-06-10 09:52:43,637 - src.models.anomaly_detector - INFO - Binary model loaded from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\rf_model_binary.joblib
2025-06-10 09:52:43,647 - src.models.anomaly_detector - INFO - Binary scaler loaded from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\standard_scaler.joblib
2025-06-10 09:52:43,683 - src.models.anomaly_detector - INFO - Multiclass model loaded from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-10 09:52:43,683 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-10 09:52:43,683 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-10 09:52:43,683 - control_panel - INFO - Models loaded successfully
2025-06-10 09:52:44,347 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-10 09:52:44,383 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-10 09:52:45,193 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
