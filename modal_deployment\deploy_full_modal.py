#!/usr/bin/env python3
"""
Deploy script for Modal-hosted 5G Security Analytics with Gradio
Complete deployment including models and UI
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def check_modal_installed():
    """Check if Modal is installed"""
    try:
        result = subprocess.run(["modal", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Modal CLI installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Modal CLI not found")
            return False
    except FileNotFoundError:
        print("❌ Modal CLI not found")
        return False

def install_modal():
    """Install Modal CLI"""
    print("📦 Installing Modal CLI...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "modal"], check=True)
        print("✅ Modal CLI installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install Modal CLI")
        return False

def setup_modal_auth():
    """Setup Modal authentication"""
    print("🔐 Setting up Modal authentication...")
    print("Please run: modal setup")
    print("This will open a browser window to authenticate with Modal")
    
    response = input("Have you completed Modal authentication? (y/n): ")
    return response.lower() == 'y'

def check_models_exist():
    """Check if models exist locally"""
    models_path = Path("models/rf_models")
    if not models_path.exists():
        print("❌ Models directory not found: models/rf_models")
        print("Please train your models first using the existing pipeline")
        return False
    
    required_files = [
        "model_binary/rf_model_binary.joblib",
        "model_binary/standard_scaler.joblib",
        "model_multiclass/rf_model_multiclass.joblib",
        "model_multiclass/standard_scaler.joblib"
    ]
    
    missing_files = []
    for file in required_files:
        full_path = models_path / file
        if not full_path.exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing model files: {missing_files}")
        return False
    
    print("✅ All required model files found")
    return True

def create_volumes():
    """Create Modal volumes if they don't exist"""
    print("📦 Creating Modal volumes...")
    
    volumes = ["5g-models", "5g-state"]
    
    for volume_name in volumes:
        try:
            # Try to create volume (will succeed if it doesn't exist)
            result = subprocess.run([
                "modal", "volume", "create", volume_name
            ], capture_output=True, text=True, encoding='utf-8', errors='replace')
            
            if result.returncode == 0:
                print(f"✅ Volume '{volume_name}' created successfully")
            else:
                # Volume might already exist, check the error
                if "already exists" in result.stderr.lower():
                    print(f"✅ Volume '{volume_name}' already exists")
                else:
                    print(f"⚠️ Issue with volume '{volume_name}': {result.stderr}")
        except Exception as e:
            print(f"❌ Error creating volume '{volume_name}': {e}")
    
    return True

def upload_models():
    """Upload models to Modal volume"""
    print("📤 Uploading models to Modal volume...")
    try:
        # First, ensure the volume exists
        create_volumes()
        
        result = subprocess.run([
            "modal", "volume", "put", "5g-models", 
            "models/rf_models", "/models"
        ], capture_output=True, text=True, encoding='utf-8', errors='replace')
        
        if result.returncode == 0:
            print("✅ Models uploaded successfully to Modal volume")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print("❌ Model upload failed")
            if result.stderr:
                print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error uploading models: {e}")
        return False

def deploy_full_app():
    """Deploy full Modal application with Gradio UI"""
    print("🚀 Deploying full Modal application with Gradio UI...")
    try:
        result = subprocess.run([
            "modal", "deploy", "modal_full_app.py"
        ], capture_output=True, text=True, encoding='utf-8', errors='replace')
        
        if result.returncode == 0:
            print("✅ Full application deployed successfully")
            if result.stdout:
                print(result.stdout)
            
            # Extract URL from output
            lines = result.stdout.split('\n')
            for line in lines:
                if "https://" in line and "modal.run" in line:
                    url = line.strip()
                    print(f"🌐 Application URL: {url}")
                    return url
            return True
        else:
            print("❌ Application deployment failed")
            if result.stderr:
                print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error deploying application: {e}")
        return False

def test_app(app_url):
    """Test deployed application"""
    if not app_url or not isinstance(app_url, str):
        print("⚠️ No application URL provided, skipping test")
        return True
    
    print("🧪 Testing deployed application...")
    try:
        # Test the Gradio interface
        response = requests.get(app_url, timeout=30)
        if response.status_code == 200:
            print("✅ Gradio interface is accessible")
            return True
        else:
            print(f"❌ Application test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Application test failed: {e}")
        return False

def main():
    """Main deployment function"""
    print("🛡️ 5G Security Analytics - Full Modal Deployment")
    print("=" * 60)
    print("🏆 Hugging Face Agents-MCP-Hackathon Track 3")
    print("🚀 Deploying Complete Application to Modal")
    print("🎛️ Includes Backend + Gradio Frontend")
    print("💰 Optimized for economical serverless usage")
    print()
    
    # Step 1: Check Modal CLI
    if not check_modal_installed():
        if not install_modal():
            print("❌ Cannot proceed without Modal CLI")
            return
    
    # Step 2: Check authentication
    if not setup_modal_auth():
        print("❌ Modal authentication required")
        return
    
    # Step 3: Check models
    if not check_models_exist():
        print("❌ Please train your models first")
        print("Run: python demo.py or python test_system.py")
        return
    
    # Step 4: Upload models
    if not upload_models():
        print("❌ Model upload failed")
        return
    
    # Step 5: Deploy full application
    app_url = deploy_full_app()
    if not app_url:
        print("❌ Application deployment failed")
        return
    
    # Step 6: Test application
    if not test_app(app_url):
        print("⚠️ Application test failed, but deployment may still work")
    
    print("\n🎉 Full deployment completed successfully!")
    print("=" * 60)
    print("✅ Complete 5G Security Analytics application is now running on Modal")
    if isinstance(app_url, str):
        print(f"🌐 Access your application: {app_url}")
    print()
    print("🎛️ Features available:")
    print("  • Real-time 5G network anomaly detection")
    print("  • Interactive Gradio control panel")
    print("  • Background batch processing")
    print("  • Live statistics and monitoring")
    print()
    print("💰 Serverless benefits:")
    print("  • Pay only for actual usage time")
    print("  • Automatic scaling")
    print("  • No server management required")
    print("🏆 Perfect for Hugging Face Agents-MCP-Hackathon!")

if __name__ == "__main__":
    main()
