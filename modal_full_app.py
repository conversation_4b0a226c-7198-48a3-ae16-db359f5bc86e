#!/usr/bin/env python3
"""
5G Network Security Analytics - Modal Serverless Deployment
Real-time Network Threat Detection using 5G-NIDD Dataset and RandomForest Models

This application simulates a 5G network environment and uses machine learning
to detect malicious network traffic in real-time. Deployed on Modal platform
for scalable, serverless processing.

Features:
- Real-time anomaly detection using RandomForest models
- 5G-NIDD dataset simulation for network traffic analysis
- Dual-platform compatibility (Local + Modal)
- Professional Gradio control panel interface

"""

import modal
import os
import json
import time
from typing import Dict, Any, List

# Modal setup - 5G Security Analytics Application
app = modal.App("5g-security-analytics-full")

# Create volumes for models and state
models_volume = modal.Volume.from_name("5g-models", create_if_missing=True)
state_volume = modal.Volume.from_name("5g-state", create_if_missing=True)

# Define image with all dependencies and real 5G-NIDD dataset
image = (
    modal.Image.debian_slim()
    .pip_install([
        "fastapi",
        "uvicorn",
        "pandas",
        "numpy",
        "scikit-learn",
        "joblib",
        "gradio",
        "requests",
        "python-multipart"
    ])
    .copy_local_dir("src", "/app/src")
    .copy_local_dir("models/rf_models", "/tmp/models")
    .copy_local_file("data/sample_inputs/5g_nidd_dataset.csv", "/tmp/5g_nidd_dataset.csv")
)

# Shared state management
@app.function(
    image=image,
    volumes={"/models": models_volume, "/state": state_volume},
    timeout=300
)
def load_models():
    """Load ML models from volume"""
    import joblib
    
    models = {}
    try:
        # Load binary classification model
        binary_model_path = "/models/model_binary/rf_model_binary.joblib"
        binary_scaler_path = "/models/model_binary/standard_scaler.joblib"
        
        if os.path.exists(binary_model_path) and os.path.exists(binary_scaler_path):
            models["binary_model"] = joblib.load(binary_model_path)
            models["binary_scaler"] = joblib.load(binary_scaler_path)
        
        # Load multiclass model
        multiclass_model_path = "/models/model_multiclass/rf_model_multiclass.joblib"
        multiclass_scaler_path = "/models/model_multiclass/standard_scaler.joblib"
        
        if os.path.exists(multiclass_model_path) and os.path.exists(multiclass_scaler_path):
            models["multiclass_model"] = joblib.load(multiclass_model_path)
            models["multiclass_scaler"] = joblib.load(multiclass_scaler_path)
            
        return models
    except Exception as e:
        print(f"Error loading models: {e}")
        return {}

@app.function(
    image=image,
    volumes={"/models": models_volume, "/state": state_volume},
    timeout=600
)
def process_batch(batch_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process a batch of data"""
    import pandas as pd
    from datetime import datetime
    
    models = load_models.remote()
    
    if not models:
        return []
    
    try:
        # Convert to DataFrame
        df = pd.DataFrame(batch_data)
        
        # Feature columns (same as in original AnomalyDetector)
        feature_cols = [
            'Dur', 'Proto', 'Dir', 'State', 'sTos', 'dTos', 'TotPkts', 'TotBytes',
            'SrcBytes', 'srcUdata', 'dstUdata', 'SrcLoad', 'DstLoad', 'SrcLoss',
            'DstLoss', 'SrcJitter', 'DstJitter', 'sMaxPktSz', 'dMaxPktSz'
        ]
        
        # Ensure all feature columns exist
        for col in feature_cols:
            if col not in df.columns:
                df[col] = 0
        
        X = df[feature_cols]
        
        results = []
        for idx, row in X.iterrows():
            result = {
                "record_id": idx,
                "timestamp": datetime.now().isoformat(),
                "features": row.to_dict(),
                "is_anomaly": False,
                "anomaly_type": "Normal",
                "confidence": 0.5,
                "severity": "Low"
            }
            
            # Binary classification
            if "binary_model" in models and "binary_scaler" in models:
                X_scaled = models["binary_scaler"].transform([row.values])
                binary_pred = models["binary_model"].predict(X_scaled)[0]
                binary_prob = models["binary_model"].predict_proba(X_scaled)[0]
                
                result["is_anomaly"] = bool(binary_pred)
                result["confidence"] = float(max(binary_prob))
                
                # Multiclass classification if anomaly detected
                if binary_pred and "multiclass_model" in models:
                    multiclass_pred = models["multiclass_model"].predict(X_scaled)[0]
                    multiclass_prob = models["multiclass_model"].predict_proba(X_scaled)[0]
                    
                    # Map class to anomaly type
                    anomaly_types = ["Normal", "DoS", "Probe", "R2L", "U2R"]
                    if multiclass_pred < len(anomaly_types):
                        result["anomaly_type"] = anomaly_types[multiclass_pred]
                    
                    # Set severity based on confidence
                    max_prob = float(max(multiclass_prob))
                    if max_prob > 0.8:
                        result["severity"] = "High"
                    elif max_prob > 0.6:
                        result["severity"] = "Medium"
                    else:
                        result["severity"] = "Low"
            
            results.append(result)
        
        return results
        
    except Exception as e:
        print(f"Error processing batch: {e}")
        return []

# State management functions
@app.function(
    image=image,
    volumes={"/state": state_volume},
    timeout=60
)
def load_state():
    """Load processing state"""
    state_file = "/state/processing_state.json"
    if os.path.exists(state_file):
        try:
            with open(state_file, 'r') as f:
                return json.load(f)
        except:
            pass
    
    return {
        "is_processing": False,
        "batch_size": 100,
        "max_batches": 10,
        "requests_per_second": 10.0,
        "processing_mode": "Manual Batch",
        "current_results": [],
        "processing_stats": {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        },
        "log_messages": []
    }

@app.function(
    image=image,
    volumes={"/state": state_volume},
    timeout=60
)
def save_state(state):
    """Save processing state"""
    try:
        state_file = "/state/processing_state.json"
        os.makedirs(os.path.dirname(state_file), exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(state, f)
        state_volume.commit()
    except Exception as e:
        print(f"Error saving state: {e}")

@app.function(
    image=image,
    volumes={"/state": state_volume},
    timeout=60
)
def add_log(message: str, level: str = "INFO"):
    """Add log message"""
    state = load_state.remote()
    timestamp = time.strftime("%H:%M:%S")
    log_entry = f"[{timestamp}] {level}: {message}"
    state["log_messages"].append(log_entry)
    
    # Keep only last 100 messages
    if len(state["log_messages"]) > 100:
        state["log_messages"] = state["log_messages"][-100:]
    
    save_state.remote(state)

# Real 5G-NIDD Dataset Loading Function
@app.function(
    image=image,
    volumes={"/data": state_volume},
    timeout=120
)
def load_real_dataset_batch(batch_size: int, start_index: int = 0) -> List[Dict[str, Any]]:
    """Load real batch from 5G-NIDD dataset"""
    import pandas as pd

    try:
        # Load the real 5G-NIDD dataset
        dataset_path = "/tmp/5g_nidd_dataset.csv"  # Will be copied during image build

        # Read specific batch from CSV
        df = pd.read_csv(dataset_path, skiprows=range(1, start_index + 1), nrows=batch_size)

        if df.empty:
            print(f"No more data available from index {start_index}")
            return []

        # Convert to the format expected by our models
        batch_data = []
        for _, row in df.iterrows():
            # Map 5G-NIDD columns to our model's expected features
            record = {
                'Dur': float(row.get('dur', 0)),
                'Proto': int(row.get('prototcp', 0)) * 6 + int(row.get('protoudp', 0)) * 17 + int(row.get('protoicmp', 0)) * 1,
                'Dir': 1,  # Default direction
                'State': int(row.get('statecon', 0)) + int(row.get('statefin', 0)) * 2 + int(row.get('staterst', 0)) * 3,
                'sTos': float(row.get('stos', 0)),
                'dTos': float(row.get('dtos', 0)),
                'TotPkts': int(row.get('totpkts', 0)),
                'TotBytes': float(row.get('totbytes', 0)),
                'SrcBytes': float(row.get('srcbytes', 0)),
                'srcUdata': float(row.get('srcbytes', 0)) * 0.1,  # Estimated
                'dstUdata': float(row.get('dstbytes', 0)) * 0.1,  # Estimated
                'SrcLoad': float(row.get('srcload', 0)),
                'DstLoad': float(row.get('dstload', 0)),
                'SrcLoss': float(row.get('srcloss', 0)),
                'DstLoss': float(row.get('dstloss', 0)),
                'SrcJitter': 0.0,  # Not available in dataset
                'DstJitter': 0.0,  # Not available in dataset
                'sMaxPktSz': float(row.get('smeanpktsz', 64)),
                'dMaxPktSz': float(row.get('dmeanpktsz', 64)),
                # Store original attack type for validation
                'original_attack_type': str(row.get('attack type', 'Normal')),
                'is_malicious': int(row.get('labelmalicious', 0))
            }
            batch_data.append(record)

        print(f"Loaded {len(batch_data)} real records from 5G-NIDD dataset (starting from index {start_index})")
        return batch_data

    except Exception as e:
        print(f"Error loading real dataset: {e}")
        return []

# Background processing function using real 5G-NIDD dataset
@app.function(
    image=image,
    volumes={"/models": models_volume, "/state": state_volume},
    timeout=1800  # 30 minutes
)
def process_batches_background(batch_size: int, max_batches: int, requests_per_second: float):
    """Process batches from real 5G-NIDD dataset in background"""
    add_log.remote("Background processing started - Using real 5G-NIDD dataset")

    try:
        inter_record_delay = 1.0 / requests_per_second if requests_per_second > 0 else 0.01
        current_dataset_index = 0  # Track position in dataset

        for batch_num in range(max_batches):
            state = load_state.remote()
            if not state["is_processing"]:
                add_log.remote("Processing stopped by user")
                break

            # Load real batch data from 5G-NIDD dataset
            batch_data = load_real_dataset_batch.remote(batch_size, current_dataset_index)

            # Check if we've reached end of dataset
            if not batch_data:
                add_log.remote("Reached end of 5G-NIDD dataset, stopping processing")
                break

            current_dataset_index += batch_size

            # Update current batch
            state["processing_stats"]["current_batch"] = batch_num + 1
            save_state.remote(state)

            # Process batch
            start_time = time.time()
            batch_results = process_batch.remote(batch_data)
            processing_time = time.time() - start_time

            # Update results and stats
            state = load_state.remote()
            state["current_results"].extend(batch_results)

            # Keep only last 1000 results
            if len(state["current_results"]) > 1000:
                state["current_results"] = state["current_results"][-1000:]

            anomaly_count = sum(1 for r in batch_results if r.get("is_anomaly", False))

            state["processing_stats"]["total_processed"] += len(batch_results)
            state["processing_stats"]["total_anomalies"] += anomaly_count
            state["processing_stats"]["processing_rate"] = len(batch_results) / processing_time if processing_time > 0 else 0

            save_state.remote(state)

            add_log.remote(f"Real 5G-NIDD Batch {batch_num + 1}: {len(batch_results)} records, {anomaly_count} anomalies detected")

            time.sleep(inter_record_delay * len(batch_results))

        # Mark processing as complete
        state = load_state.remote()
        state["is_processing"] = False
        save_state.remote(state)

        add_log.remote("Real 5G-NIDD dataset processing completed")

    except Exception as e:
        add_log.remote(f"Error in background processing: {e}", "ERROR")
        state = load_state.remote()
        state["is_processing"] = False
        save_state.remote(state)

# Gradio interface
@app.function(
    image=image,
    volumes={"/models": models_volume, "/state": state_volume},
    timeout=3600,
    allow_concurrent_inputs=100
)
@modal.web_endpoint()
def gradio_app():
    """Create and return Gradio interface"""
    import gradio as gr
    
    def get_system_status():
        """Get current system status"""
        try:
            models = load_models.remote()
            dataset_status = "✅ Modal backend connected"
            model_status = f"✅ Modal models: {'Loaded' if models else 'Not loaded'}"
        except:
            dataset_status = "❌ Modal backend error"
            model_status = "❌ Cannot check models"
        
        # Processing status
        state = load_state.remote()
        if state["is_processing"]:
            processing_status = f"🔄 Processing (Modal)... Batch {state['processing_stats']['current_batch']}"
        else:
            processing_status = "⏸️ Ready (Modal)"
        
        return dataset_status, model_status, processing_status, ""
    
    def start_processing(batch_size: int, max_batches: int, requests_per_second: float, processing_mode: str):
        """Start batch processing"""
        state = load_state.remote()
        if state["is_processing"]:
            return get_system_status()[:-1] + ("⚠️ Already processing",)
        
        # Clear previous results and update configuration
        state.update({
            "is_processing": True,
            "batch_size": batch_size,
            "max_batches": max_batches,
            "requests_per_second": requests_per_second,
            "processing_mode": processing_mode,
            "current_results": [],
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            }
        })
        save_state.remote(state)
        
        add_log.remote(f"Starting Modal processing: {batch_size} records/batch, {max_batches} batches max")
        
        # Start background processing
        process_batches_background.spawn(batch_size, max_batches, requests_per_second)
        
        return get_system_status()[:-1] + ("🚀 Modal processing started",)
    
    def stop_processing():
        """Stop processing"""
        state = load_state.remote()
        state["is_processing"] = False
        save_state.remote(state)
        
        add_log.remote("Processing stopped by user")
        return get_system_status()[:-1] + ("⏹️ Processing stopped",)
    
    def clear_results():
        """Clear all results and reset stats"""
        state = load_state.remote()
        state.update({
            "current_results": [],
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            },
            "log_messages": []
        })
        save_state.remote(state)
        
        add_log.remote("Results and statistics cleared")
        return get_system_status()[:-1] + ("🧹 Results cleared",)
    
    def get_current_stats():
        """Get current processing statistics"""
        state = load_state.remote()
        stats = state.get("processing_stats", {})
        
        return f"""Current Statistics:
• Total Processed: {stats.get('total_processed', 0):,} records
• Total Anomalies: {stats.get('total_anomalies', 0):,}
• Current Batch: {stats.get('current_batch', 0)}
• Processing Rate: {stats.get('processing_rate', 0):.1f} records/sec
• Anomaly Rate: {(stats.get('total_anomalies', 0) / max(stats.get('total_processed', 1), 1) * 100):.1f}%
• Status: {'🔄 Processing' if state.get('is_processing', False) else '⏸️ Ready'}

Current Configuration:
• Batch Size: {state.get('batch_size', 100)}
• Max Batches: {state.get('max_batches', 10)}
• Requests/Second: {state.get('requests_per_second', 10.0)}
• Processing Mode: {state.get('processing_mode', 'Manual Batch')}"""
    
    # Create professional Gradio interface with clean design
    with gr.Blocks(
        title="5G Network Security Analytics - Modal Serverless Control Panel",
        css="""
        .gradio-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        """
    ) as demo:
        gr.Markdown("# 🛡️ 5G Network Security Analytics - Modal Control Panel")
        gr.Markdown("**Real-time Network Threat Detection using Authentic 5G-NIDD Dataset**")
        gr.Markdown("🚀 **Serverless ML Processing on Modal Platform** | 🏆 **Hugging Face Agents-MCP-Hackathon Track 3**")
        gr.Markdown("---")
        gr.Markdown("**System Overview**: This system processes the **real 5G-NIDD (Network Intrusion Detection Dataset)** containing **415,891 authentic 5G network records** and employs trained RandomForest machine learning models to detect malicious network traffic in real-time. All processing runs on Modal's serverless infrastructure for optimal scalability and cost efficiency.")
        gr.Markdown("📊 **Dataset**: 415,891 real 5G network traffic records | 🧠 **Models**: Pre-trained RandomForest (Binary + Multiclass) | ⚡ **Processing**: Real-time batch analysis")
        
        # System Status
        gr.Markdown("## System Status")
        
        with gr.Row():
            with gr.Column():
                dataset_status = gr.Textbox(label="Dataset Status", interactive=False)
                model_status = gr.Textbox(label="Model Status", interactive=False)
            with gr.Column():
                processing_status = gr.Textbox(label="Processing Status", interactive=False)
                operation_status = gr.Textbox(label="Last Operation", interactive=False)
        
        # Configuration Section - Real 5G-NIDD Dataset Processing
        gr.Markdown("## 🔧 Processing Configuration")
        gr.Markdown("Configure real 5G-NIDD dataset processing and ML analysis parameters")

        with gr.Row():
            with gr.Column():
                batch_size = gr.Slider(
                    minimum=10, maximum=100, value=100, step=10,
                    label="Batch Size (Records per Batch)",
                    info="Number of real 5G-NIDD records to process simultaneously (415,891 total records available)"
                )
                max_batches = gr.Slider(
                    minimum=1, maximum=50, value=10, step=1,
                    label="Maximum Batches",
                    info="Total number of batches to process from real 5G-NIDD dataset"
                )
            with gr.Column():
                requests_per_second = gr.Slider(
                    minimum=0.1, maximum=50.0, value=10.0, step=0.1,
                    label="Processing Rate (Records/Second)",
                    info="Real 5G-NIDD dataset processing rate for analysis"
                )
                processing_mode = gr.Dropdown(
                    choices=["Manual Batch", "Continuous Stream", "Sequential Analysis"],
                    value="Manual Batch",
                    label="Processing Mode",
                    info="Select how to process real 5G-NIDD dataset records"
                )
        
        # Control Buttons - Professional Interface Design
        gr.Markdown("## 🎛️ System Controls")
        gr.Markdown("Start, stop, and manage real 5G-NIDD dataset security analysis")

        with gr.Row():
            start_btn = gr.Button("🚀 Start Real 5G-NIDD Analysis", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ Stop Analysis", variant="secondary", size="lg")
            clear_btn = gr.Button("🧹 Clear Results", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 Refresh Status", variant="secondary", size="lg")
        
        # Statistics and Monitoring
        gr.Markdown("## 📊 Real-time Analytics & Performance Metrics")
        gr.Markdown("Monitor real 5G-NIDD dataset analysis performance and threat detection statistics")
        stats_display = gr.Textbox(
            label="Live Processing Statistics",
            lines=15,
            interactive=False,
            value=get_current_stats()
        )
        
        # Event handlers
        start_btn.click(
            start_processing,
            inputs=[batch_size, max_batches, requests_per_second, processing_mode],
            outputs=[dataset_status, model_status, processing_status, operation_status]
        )
        
        stop_btn.click(
            stop_processing,
            outputs=[dataset_status, model_status, processing_status, operation_status]
        )
        
        clear_btn.click(
            clear_results,
            outputs=[dataset_status, model_status, processing_status, operation_status]
        )
        
        refresh_btn.click(
            get_system_status,
            outputs=[dataset_status, model_status, processing_status, operation_status]
        )
        
        refresh_btn.click(get_current_stats, outputs=[stats_display])
        
        # Auto-refresh every 5 seconds
        demo.load(
            get_system_status,
            outputs=[dataset_status, model_status, processing_status, operation_status],
            every=5
        )
        demo.load(get_current_stats, outputs=[stats_display], every=5)
    
    return demo

if __name__ == "__main__":
    # For local testing and development
    # Note: Use 'modal deploy modal_full_app.py' for production deployment
    print("🛡️ 5G Network Security Analytics - Modal Application")
    print("📋 To deploy: modal deploy modal_full_app.py")
    print("🌐 To run locally: Use local_deployment/ folder instead")
