"""
Gradio Control Panel for 5G Security Analytics - LOCAL VERSION
"""

import gradio as gr
import json
import os
import sys
import time
import threading
import logging
import traceback
from pathlib import Path
from typing import Dict, Any, Tuple, List

# Add src to path
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.core.data_loader import DataLoader
from src.core.batch_processor import BatchProcessor
from src.models.anomaly_detector import AnomalyDetector

# Local Configuration - NO MODAL
USE_MODAL_BACKEND = False  # Always False for local deployment

logger = logging.getLogger(__name__)

# Shared state file
STATE_FILE = "processing_state_local.json"

def load_state():
    """Load processing state from file"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r') as f:
                return json.load(f)
        except:
            pass
    return {
        "is_processing": False,
        "batch_size": 100,
        "max_batches": 10,
        "requests_per_second": 10.0,
        "processing_mode": "Manual Batch",
        "current_results": [],
        "processing_stats": {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        },
        "log_messages": []
    }

def save_state(state):
    """Save processing state to file"""
    try:
        with open(STATE_FILE, 'w') as f:
            json.dump(state, f)
    except Exception as e:
        logger.error(f"Error saving state: {e}")

def add_log(message: str, level: str = "INFO"):
    """Add log message to state"""
    state = load_state()
    timestamp = time.strftime("%H:%M:%S")
    log_entry = f"[{timestamp}] {level}: {message}"
    state["log_messages"].append(log_entry)
    
    # Keep only last 100 messages
    if len(state["log_messages"]) > 100:
        state["log_messages"] = state["log_messages"][-100:]
    
    save_state(state)
    logger.info(message)

class ControlPanel:
    """
    Gradio control panel for managing processing - LOCAL ONLY
    """

    def __init__(self):
        """Initialize control panel"""
        self.data_loader = None
        self.detector = None
        self.batch_processor = None
        self.processing_thread = None
        self.use_modal = False  # Always False

        # Initialize components
        self.initialize_components()

    def initialize_components(self):
        """Initialize data loader, detector, and batch processor"""
        try:
            add_log("Using LOCAL backend for processing")

            # Get absolute paths relative to project root
            project_root = Path(__file__).parent.parent
            data_path = project_root / "data" / "sample_inputs" / "5g_nidd_dataset.csv"
            models_path = project_root / "models" / "rf_models"

            add_log(f"Project root: {project_root}")
            add_log(f"Looking for dataset at: {data_path}")
            add_log(f"Looking for models at: {models_path}")

            # Initialize data loader
            if data_path.exists():
                self.data_loader = DataLoader(str(data_path))
                add_log("Data loader initialized successfully")
            else:
                add_log(f"Dataset not found at: {data_path}", "ERROR")
                return

            # Initialize detector
            if models_path.exists():
                self.detector = AnomalyDetector(str(models_path))
                if hasattr(self.detector, 'binary_model') and self.detector.binary_model is not None:
                    self.batch_processor = BatchProcessor(self.detector)
                    add_log("Models loaded successfully")
                else:
                    add_log("Models failed to load", "ERROR")
            else:
                add_log(f"Models directory not found at: {models_path}", "ERROR")

        except Exception as e:
            add_log(f"Error initializing components: {e}", "ERROR")
            add_log(f"Traceback: {traceback.format_exc()}", "ERROR")

    def get_system_status(self) -> Tuple[str, str, str]:
        """Get current system status"""
        # Local backend status only
        if self.data_loader and hasattr(self.data_loader, 'df') and self.data_loader.df is not None:
            dataset_info = self.data_loader.get_dataset_info()
            dataset_status = f"✅ Dataset loaded: {dataset_info['total_records']:,} records"
        else:
            dataset_status = "❌ Dataset not loaded"

        if self.detector and hasattr(self.detector, 'binary_model') and self.detector.binary_model is not None:
            model_status = "✅ Local models loaded"
        else:
            model_status = "❌ Local models not loaded"

        # Processing status
        state = load_state()
        if state["is_processing"]:
            processing_status = f"🔄 Processing (Local)... Batch {state['processing_stats']['current_batch']}"
        else:
            processing_status = "⏸️ Ready (Local)"

        return dataset_status, model_status, processing_status
    
    def start_processing(self, batch_size: int, max_batches: int,
                        requests_per_second: float, processing_mode: str) -> Tuple[str, str, str, str]:
        """Start batch processing"""
        # Check local components
        if not self.data_loader or not self.batch_processor:
            add_log("Cannot start processing: local components not initialized", "ERROR")
            return self.get_system_status() + ("❌ Cannot start: components not ready",)

        state = load_state()
        if state["is_processing"]:
            add_log("Processing already running", "WARNING")
            return self.get_system_status() + ("⚠️ Already processing",)

        # Reset data loader position
        self.data_loader.reset_stream()

        # Clear previous results and update configuration
        state.update({
            "is_processing": True,
            "batch_size": batch_size,
            "max_batches": max_batches,
            "requests_per_second": requests_per_second,
            "processing_mode": processing_mode,
            "current_results": [],
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            }
        })
        save_state(state)

        # Start processing thread
        self.processing_thread = threading.Thread(
            target=self._process_batches,
            args=(batch_size, max_batches, requests_per_second),
            daemon=True
        )
        self.processing_thread.start()

        add_log(f"Started local processing: {batch_size} per batch, {max_batches} batches max")
        return self.get_system_status() + ("🚀 Processing started (Local)",)

    def stop_processing(self) -> Tuple[str, str, str, str]:
        """Stop batch processing"""
        state = load_state()
        state["is_processing"] = False
        save_state(state)

        add_log("Processing stopped by user")
        return self.get_system_status() + ("⏹️ Processing stopped",)

    def _process_batches(self, batch_size: int, max_batches: int, requests_per_second: float):
        """Process batches locally"""
        try:
            add_log(f"Starting local batch processing: batch_size={batch_size}, max_batches={max_batches}")
            
            if not self.batch_processor:
                add_log("Batch processor not initialized", "ERROR")
                return

            for batch_num in range(max_batches):
                state = load_state()
                if not state["is_processing"]:
                    add_log("Processing stopped by user", "INFO")
                    break

                start_time = time.time()

                # Get next batch from data loader
                batch_data = self.data_loader.get_batch(batch_size)
                if not batch_data:
                    add_log("No more data available", "INFO")
                    break

                add_log(f"Processing batch {batch_num + 1} with {len(batch_data)} records")

                # Process batch with local batch processor
                batch_results = self.batch_processor.process_batch_sequential(batch_data)

                # Update state with results
                state = load_state()
                state["current_results"].extend(batch_results)
                state["processing_stats"]["total_processed"] += len(batch_data)
                state["processing_stats"]["total_anomalies"] += sum(1 for r in batch_results if r.get("anomaly_detected", False))
                state["processing_stats"]["current_batch"] = batch_num + 1

                processing_time = time.time() - start_time
                state["processing_stats"]["processing_rate"] = len(batch_data) / processing_time if processing_time > 0 else 0

                save_state(state)

                add_log(f"Batch {batch_num + 1} completed: {len(batch_results)} results, "
                       f"{processing_time:.2f}s processing time")

                # Rate limiting
                if requests_per_second > 0:
                    sleep_time = 1.0 / requests_per_second
                    time.sleep(sleep_time)

            # Mark processing as complete
            state = load_state()
            state["is_processing"] = False
            save_state(state)
            add_log("Batch processing completed")

        except Exception as e:
            add_log(f"Error in batch processing: {e}", "ERROR")
            add_log(f"Traceback: {traceback.format_exc()}", "ERROR")
            
            # Mark processing as stopped
            state = load_state()
            state["is_processing"] = False
            save_state(state)

    def get_results(self) -> str:
        """Get current processing results"""
        state = load_state()
        
        if not state["current_results"]:
            return "No results available"

        # Format results summary
        stats = state["processing_stats"]
        summary = f"""
## Processing Summary (Local)

**Statistics:**
- Total Processed: {stats['total_processed']:,} records
- Total Anomalies: {stats['total_anomalies']:,}
- Current Batch: {stats['current_batch']}
- Processing Rate: {stats['processing_rate']:.2f} records/sec

**Recent Results:** ({len(state['current_results'])} total)
"""

        # Show last 10 results
        recent_results = state["current_results"][-10:]
        for i, result in enumerate(recent_results):
            anomaly_status = "🚨 ANOMALY" if result.get("anomaly_detected", False) else "✅ Normal"
            summary += f"\n{len(state['current_results']) - len(recent_results) + i + 1}. {anomaly_status} - Binary: {result.get('binary_prediction', 'N/A')}, Multi: {result.get('multiclass_prediction', 'N/A')}"

        return summary

    def get_logs(self) -> str:
        """Get current log messages"""
        state = load_state()
        if not state["log_messages"]:
            return "No log messages"
        
        return "\n".join(state["log_messages"][-50:])  # Last 50 messages

def create_control_panel():
    """Create the Gradio control panel interface"""
    panel = ControlPanel()

    with gr.Blocks(title="5G Security Analytics - Local Control Panel", theme=gr.themes.Soft()) as interface:
        gr.Markdown("# 🛡️ 5G Security Analytics - Local Control Panel")
        gr.Markdown("*Processing data locally using your machine's resources*")

        with gr.Tab("Control Panel"):
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("## System Status")
                    
                    dataset_status = gr.Textbox(
                        label="Dataset Status",
                        interactive=False,
                        value="Checking..."
                    )
                    model_status = gr.Textbox(
                        label="Model Status", 
                        interactive=False,
                        value="Checking..."
                    )
                    processing_status = gr.Textbox(
                        label="Processing Status",
                        interactive=False,
                        value="Checking..."
                    )

                with gr.Column(scale=1):
                    gr.Markdown("## Processing Configuration")
                    
                    batch_size = gr.Slider(
                        minimum=10,
                        maximum=1000,
                        value=100,
                        step=10,
                        label="Batch Size"
                    )
                    
                    max_batches = gr.Slider(
                        minimum=1,
                        maximum=100,
                        value=10,
                        step=1,
                        label="Max Batches"
                    )
                    
                    requests_per_second = gr.Slider(
                        minimum=1.0,
                        maximum=50.0,
                        value=10.0,
                        step=1.0,
                        label="Processing Rate (batches/sec)"
                    )
                    
                    processing_mode = gr.Dropdown(
                        choices=["Manual Batch", "Continuous Stream"],
                        value="Manual Batch",
                        label="Processing Mode"
                    )

            with gr.Row():
                start_btn = gr.Button("🚀 Start Processing", variant="primary", size="lg")
                stop_btn = gr.Button("⏹️ Stop Processing", variant="stop", size="lg")
                refresh_btn = gr.Button("🔄 Refresh Status", size="lg")

            with gr.Row():
                processing_message = gr.Textbox(
                    label="Processing Message",
                    interactive=False,
                    value=""
                )

        with gr.Tab("Results"):
            with gr.Row():
                results_refresh_btn = gr.Button("🔄 Refresh Results")
            
            results_display = gr.Markdown(value="No results available")

        with gr.Tab("Logs"):
            with gr.Row():
                logs_refresh_btn = gr.Button("🔄 Refresh Logs")
                
            logs_display = gr.Textbox(
                label="System Logs",
                lines=20,
                interactive=False,
                value="No logs available"
            )

        # Event handlers
        def refresh_status():
            return panel.get_system_status()

        def start_processing_handler(batch_size, max_batches, requests_per_second, processing_mode):
            return panel.start_processing(batch_size, max_batches, requests_per_second, processing_mode)

        def stop_processing_handler():
            return panel.stop_processing()

        def refresh_results():
            return panel.get_results()

        def refresh_logs():
            return panel.get_logs()

        # Bind events
        refresh_btn.click(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status]
        )

        start_btn.click(
            fn=start_processing_handler,
            inputs=[batch_size, max_batches, requests_per_second, processing_mode],
            outputs=[dataset_status, model_status, processing_status, processing_message]
        )

        stop_btn.click(
            fn=stop_processing_handler,
            outputs=[dataset_status, model_status, processing_status, processing_message]
        )

        results_refresh_btn.click(
            fn=refresh_results,
            outputs=[results_display]
        )

        logs_refresh_btn.click(
            fn=refresh_logs,
            outputs=[logs_display]
        )

        # Auto-refresh status on load
        interface.load(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status]
        )

    return interface

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    interface = create_control_panel()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False
    )
