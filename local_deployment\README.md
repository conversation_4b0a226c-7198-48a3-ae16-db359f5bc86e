# 5G Security Analytics - LOCAL DEPLOYMENT

## 🏠 Tam Local Çalışan Sistem

Bu klasör **tamamen local** olarak çalışan 5G Security Analytics sistemini içerir. Hiçbir external servis veya cloud platformu gerektirmez.

### 📁 Dosyalar:
- `app.py` - Gradio ana uygulama (local)
- `control_panel.py` - Gradio kontrol paneli (local backend)
- `dashboard.py` - Streamlit dashboard

### 🚀 Çalıştırma:

#### Gereksinimler:
```bash
cd c:\Users\<USER>\Documents\hg-hackathon25-track3
pip install gradio streamlit pandas numpy scikit-learn joblib
```

#### Gradio Control Panel:
```bash
cd local_deployment
python app.py
```
- 🌐 URL: http://localhost:7860
- 🎛️ Tam local çalışır
- 📊 Model loading: `../models/rf_models/`
- 📁 Data: `../data/sample_inputs/5g_nidd_dataset.csv`

#### Streamlit Dashboard:
```bash
cd local_deployment
streamlit run dashboard.py
```
- 🌐 URL: http://localhost:8501
- 📊 Real-time monitoring
- 📁 State file: `processing_state_local.json`

### ✅ Avantajlar:
- 🏠 Tamamen local, internet gerektirmez
- 🔒 Veriler local'de kalır
- 💰 Hiçbir maliyet yok
- ⚡ Hızlı başlangıç
- 🔧 Kolay debug

### 📝 Notlar:
- Models `../models/rf_models/` klasöründe olmalı
- Dataset `../data/sample_inputs/5g_nidd_dataset.csv` dosyasında olmalı
- State dosyası: `processing_state_local.json`
