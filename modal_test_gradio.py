#!/usr/bin/env python3
"""
Simplified Modal-hosted Gradio App for testing
"""

import modal

app = modal.App("5g-security-test")

# Simple image with just gradio
image = modal.Image.debian_slim().pip_install(["gradio", "requests"])

@app.function(
    image=image,
    timeout=3600,
    allow_concurrent_inputs=100
)
@modal.web_endpoint()
def gradio_test():
    """Simple Gradio test interface"""
    import gradio as gr
    
    def hello_world(name):
        return f"Hello {name}! Modal + Gradio is working!"
    
    def get_status():
        return "✅ Modal is working", "✅ Gradio is working", "✅ Everything OK"
    
    # Create simple interface
    with gr.<PERSON>s(title="5G Security Analytics - Test") as demo:
        gr.Markdown("# 5G Security Analytics - Modal Test")
        gr.Markdown("Testing Modal + Gradio integration")
        
        with gr.Row():
            name_input = gr.Textbox(label="Your Name", value="User")
            output = gr.Textbox(label="Response")
        
        with gr.<PERSON>():
            test_btn = gr.<PERSON><PERSON>("Test Connection", variant="primary")
            status_btn = gr.<PERSON><PERSON>("Check Status")
        
        with gr.<PERSON>():
            status1 = gr.Textbox(label="Modal Status")
            status2 = gr.Textbox(label="Gradio Status") 
            status3 = gr.Textbox(label="Overall Status")
        
        # Event handlers
        test_btn.click(hello_world, inputs=[name_input], outputs=[output])
        status_btn.click(get_status, outputs=[status1, status2, status3])
        
        # Auto-load status
        demo.load(get_status, outputs=[status1, status2, status3])
    
    return demo

if __name__ == "__main__":
    app.serve()
