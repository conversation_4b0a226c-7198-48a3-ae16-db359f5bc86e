#!/usr/bin/env python3
"""
Test component initialization for local deployment
"""

import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.data_loader import DataLoader
from src.models.anomaly_detector import AnomalyDetector
from src.core.batch_processor import BatchProcessor

def test_components():
    print("Testing Local Deployment Component Initialization")
    print("=" * 60)
    
    # Test paths
    project_root = Path(__file__).parent.parent
    data_path = project_root / "data" / "sample_inputs" / "5g_nidd_dataset.csv"
    models_path = project_root / "models" / "rf_models"

    print(f"Project root: {project_root.absolute()}")
    print(f"Data path exists: {data_path.exists()} - {data_path.absolute()}")
    print(f"Models path exists: {models_path.exists()} - {models_path.absolute()}")
    print()

    # Test DataLoader
    data_loader = None
    if data_path.exists():
        print("Testing DataLoader...")
        try:
            data_loader = DataLoader(str(data_path))
            print(f"✅ DataLoader initialized: {data_loader is not None}")
            print(f"✅ Data loaded: {hasattr(data_loader, 'df') and data_loader.df is not None}")
            if hasattr(data_loader, 'df') and data_loader.df is not None:
                print(f"✅ Dataset shape: {data_loader.df.shape}")
        except Exception as e:
            print(f"❌ DataLoader failed: {e}")
    else:
        print("❌ Data file not found")
    
    print()

    # Test AnomalyDetector
    detector = None
    if models_path.exists():
        print("Testing AnomalyDetector...")
        try:
            detector = AnomalyDetector(str(models_path))
            print(f"✅ Detector initialized: {detector is not None}")
            print(f"✅ Binary model loaded: {hasattr(detector, 'binary_model') and detector.binary_model is not None}")
            print(f"✅ Multiclass model loaded: {hasattr(detector, 'multiclass_model') and detector.multiclass_model is not None}")
        except Exception as e:
            print(f"❌ AnomalyDetector failed: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ Models directory not found")
    
    print()

    # Test BatchProcessor
    if data_loader and detector:
        print("Testing BatchProcessor...")
        try:
            batch_processor = BatchProcessor(detector)
            print(f"✅ BatchProcessor initialized: {batch_processor is not None}")
        except Exception as e:
            print(f"❌ BatchProcessor failed: {e}")
    else:
        print("❌ Cannot test BatchProcessor: missing dependencies")

    print()
    print("Component test completed!")

if __name__ == "__main__":
    test_components()
