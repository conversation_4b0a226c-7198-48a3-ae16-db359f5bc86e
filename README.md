# 🛡️ 5G Security Analytics System

**Real-time Network Security Threat Detection for 5G Networks**

🏆 **Hugging Face Agents-MCP-Hackathon Track 3 Project**

## 🚀 Quick Start

Choose your deployment approach:

```bash
python launcher.py
```

## 🚀 Features

- **Real-time Anomaly Detection**: Binary and multiclass classification using RandomForest models
- **Interactive Dashboard**: Modern Gradio-based web interface
- **Batch Processing**: Efficient processing of large datasets
- **Performance Monitoring**: Real-time metrics and throughput tracking
- **Attack Classification**: Detects UDPFlood, HTTPFlood, SYNScan, and other attack types
- **GPU Ready**: Optional NVIDIA RAPIDS support for acceleration

## 📁 Project Structure

```
├── src/
│   ├── core/
│   │   ├── data_loader.py      # Dataset loading and streaming
│   │   └── batch_processor.py  # Batch processing engine
│   ├── models/
│   │   └── anomaly_detector.py # RandomForest-based detection
│   ├── ui/
│   │   └── dashboard.py        # Gradio dashboard interface
│   └── utils/
│       └── metrics.py          # Performance metrics
├── models/
│   └── rf_models/              # Pre-trained RandomForest models
├── data/
│   └── sample_inputs/          # 5G-NIDD dataset
├── reference_project/          # Original implementation reference
├── app.py                      # Main application
├── test_system.py             # System tests
└── requirements.txt           # Dependencies
```

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- NVIDIA GPU (optional, for acceleration)
- Modal account (for serverless backend - recommended for hackathon)

### Setup Options

#### 🚀 Option 1: Modal Serverless Backend (Recommended for Hackathon)
```bash
# Clone and setup
git clone <repository-url>
cd hg-hackathon25-track3
pip install -r requirements.txt

# Train models (if not already done)
python test_system.py

# Deploy to Modal (one-time setup)
python deploy_modal.py

# Start local interfaces
python app.py  # Control panel
streamlit run dashboard.py  # Dashboard
```

#### 🏠 Option 2: Local Backend
```bash
# Clone and setup
git clone <repository-url>
cd hg-hackathon25-track3
pip install -r requirements.txt

# Verify installation
python test_system.py

# Start the system
python start_system.py
```

## 🏆 Hugging Face Agents-MCP-Hackathon Track 3

### 🚀 Modal Serverless Backend Features
- **Economical Usage**: Pay only for actual processing time
- **Auto-scaling**: Automatically scales from 0 to thousands of containers
- **RandomForest Preservation**: Your trained models work unchanged
- **Real-time Processing**: Sub-second response times
- **Professional Interface**: Clean Gradio + Streamlit UI
- **Zero Infrastructure**: No server management required

### 💰 Cost Optimization
- Container idle timeout: 60 seconds (minimal cost)
- Efficient resource allocation: 2GB RAM, 2 CPU cores
- Smart batching: Process multiple records efficiently
- Volume storage: Persistent model storage

## 🚀 Quick Start

### Launch Control Panel (Gradio)
```bash
python app.py
```
Access the control panel at `http://localhost:7860`

### Launch Monitoring Dashboard (Streamlit)
```bash
streamlit run dashboard.py
```
Access the dashboard at `http://localhost:8501`

### Complete Setup
1. **Start Control Panel**: `python app.py` (Port 7860)
2. **Start Dashboard**: `streamlit run dashboard.py` (Port 8501, another terminal)
3. **Configure & Start**: Use Gradio panel to configure and start processing
4. **Monitor**: Watch real-time results in Streamlit dashboard

### Quick Start (Both at once)
```bash
python start_system.py
```

### Command Line Options
```bash
python app.py --help
python app.py --host 0.0.0.0 --port 8080 --share
```

## 📊 Interface Features

### Gradio Simulation Control Panel (Port 7860) - Clean & Minimal
- **Simulation Interface**: Real-time Network Security Threat Detection Simulation
- **Minimalist Design**: Clean, simple interface focused on functionality
- **Essential Configuration**: Batch size (10-1000), Max batches (1-100), RPS (0.1-100)
- **Simple Controls**: Clear Start/Stop/Clear/Refresh buttons
- **Auto-refresh**: Continuous 2-second refresh for real-time updates
- **System Status**: Real-time dataset, model, and processing status
- **Live Statistics**: Current processing metrics and configuration display
- **Real-time Sync**: Instant synchronization with Streamlit dashboard

### Streamlit Dashboard (Port 8501) - Instant Real-time Updates
- **Row 1**: Anomaly Detection Timeline (large) + Traffic Distribution (pie chart)
- **Row 2**: Total Requests Over Time (like original) + Real-time Processing Rate (gauge)
- **Instant Updates**: 0.5s refresh when processing, configurable when idle
- **No Delays**: Eliminated artificial delays for immediate response
- **Perfect Sync**: Real-time synchronization with Gradio control panel
- **Live Metrics**: Processing rate, anomaly rate, throughput statistics
- **Performance Monitoring**: Real-time processing statistics and gauges
- **Recent Detections**: Latest anomaly details with confidence scores
- **System Logs**: Live processing logs and status updates
- **Responsive Design**: Clean, functional layout optimized for monitoring

## 🤖 Models

The system uses pre-trained RandomForest models:

- **Binary Classification**: Normal vs Anomaly detection
- **Multiclass Classification**: Attack type identification
  - UDPFlood, HTTPFlood, SYNScan, TCPConnectScan
  - SYNFlood, ICMPFlood, SlowrateDoS, UDPScan

### Model Performance
- **Processing Speed**: ~100-500 records/second
- **Accuracy**: High precision anomaly detection
- **Real-time**: Sub-millisecond inference per record

## 📈 Performance

### Throughput Metrics
- **CPU Mode**: 100-300 records/second
- **Batch Processing**: Optimized for high-volume data
- **Memory Efficient**: Streaming processing for large datasets

### Monitoring
- Real-time throughput calculation
- Processing time tracking
- Memory usage monitoring
- Performance statistics

## 🔧 API Usage

### Basic Usage
```python
from src.models.anomaly_detector import AnomalyDetector
from src.core.data_loader import DataLoader

# Initialize components
detector = AnomalyDetector("models/rf_models")
loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")

# Process single record
sample_data = {...}  # Your network data
result = detector.detect_anomaly(sample_data)
print(f"Anomaly: {result['is_anomaly']}, Type: {result['attack_type']}")
```

### Batch Processing
```python
from src.core.batch_processor import BatchProcessor

processor = BatchProcessor(detector)
batch_data = loader.get_batch(100)
results = processor.process_batch_sequential(batch_data.to_dict('records'))
```

## 📚 Reference

The `reference_project/` directory contains the original Streamlit-based implementation with advanced features including:
- Multi-agent architecture
- SLM integration
- RAG capabilities
- NVIDIA RAPIDS support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
