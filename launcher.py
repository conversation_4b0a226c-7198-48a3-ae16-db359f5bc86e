#!/usr/bin/env python3
"""
5G Security Analytics - Deployment Launcher
Choose between Local or Modal deployment
"""

import os
import sys
from pathlib import Path

def show_menu():
    print("🛡️ 5G Security Analytics - Deployment Launcher")
    print("=" * 60)
    print("🏆 Hugging Face Agents-MCP-Hackathon Track 3")
    print()
    print("Choose your deployment approach:")
    print()
    print("1. 🏠 LOCAL DEPLOYMENT")
    print("   • Fully local operation")
    print("   • No external dependencies")
    print("   • Instant startup")
    print("   • Zero cost")
    print()
    print("2. 🚀 MODAL DEPLOYMENT") 
    print("   • Serverless cloud deployment")
    print("   • Global accessibility")
    print("   • Auto-scaling")
    print("   • Pay-per-use")
    print()
    print("3. ❌ Exit")
    print()

def launch_local():
    print("🏠 Starting LOCAL deployment...")
    print("=" * 40)
    print()
    print("📁 Location: local_deployment/")
    print("🎛️ Gradio UI: python local_deployment/app.py")
    print("📊 Dashboard: streamlit run local_deployment/dashboard.py")
    print()
    print("🌐 URLs after start:")
    print("   • Gradio: http://localhost:7860")
    print("   • Streamlit: http://localhost:8501")
    print()
    
    choice = input("Start Gradio control panel? (y/n): ")
    if choice.lower() == 'y':
        os.chdir("local_deployment")
        os.system("python app.py")

def launch_modal():
    print("🚀 Starting MODAL deployment...")
    print("=" * 40)
    print()
    print("📁 Location: modal_deployment/")
    print("🚀 Deploy: python modal_deployment/deploy_full_modal.py")
    print()
    print("📋 Prerequisites:")
    print("   • Modal CLI installed (pip install modal)")
    print("   • Modal authentication (modal setup)")
    print("   • Models trained (in models/rf_models/)")
    print()
    
    choice = input("Start Modal deployment? (y/n): ")
    if choice.lower() == 'y':
        os.system("python modal_deployment/deploy_full_modal.py")

def main():
    while True:
        show_menu()
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == "1":
            launch_local()
            break
        elif choice == "2":
            launch_modal()
            break
        elif choice == "3":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")
            print()

if __name__ == "__main__":
    main()
