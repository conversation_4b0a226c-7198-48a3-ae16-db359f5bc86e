#!/usr/bin/env python3
"""
Setup script to upload existing RandomForest models to Modal volume
This preserves your trained models and pipeline.
Uses image.copy_local_dir to include models in the image.
"""

import modal
import os
import json
import shutil
from pathlib import Path
import sys

# Modal App definition
app = modal.App("5g-model-setup-v3")

# Image definition
# This will copy the local 'models' directory from your project root
# into '/app_setup_models' inside the image.
image = (
    modal.Image.debian_slim(python_version="3.11")
    .pip_install([
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "scikit-learn>=1.2.0",
        "joblib>=1.2.0"
    ])
    .copy_local_dir(local_path="models", remote_path="/app_setup_models")
)

# Volume for model storage
volume = modal.Volume.from_name("5g-analytics-storage", create_if_missing=True)

@app.function(
    image=image,
    volumes={"/app/storage": volume}, # Volume is still needed for the destination
    timeout=600
)
def upload_models_to_volume() -> bool: # Renamed for clarity
    """Upload existing models from the image to Modal volume"""
    print("🔄 Starting model upload to Modal volume...")

    # Source directory (copied into the image by .copy_local_dir)
    # local "models/rf_models" becomes "/app_setup_models/rf_models" in the image
    image_source_dir = Path("/app_setup_models/rf_models")

    print(f"ℹ️ Attempting to access models from image path: {image_source_dir}")
    print(f"ℹ️ Current working directory in container: {os.getcwd()}")

    if not image_source_dir.exists():
        print(f"❌ Source models directory not found at image path: {image_source_dir}")
        
        # Debug: List contents of the base path where models were copied
        base_copied_path = Path("/app_setup_models")
        if base_copied_path.exists():
            print(f"ℹ️ Contents of base image copy path ({base_copied_path}):")
            try:
                for item in base_copied_path.iterdir():
                    print(f"  - {item.name}{'/' if item.is_dir() else ''}")
            except Exception as e:
                print(f"  Could not list contents of {base_copied_path}: {e}")
        else:
            print(f"ℹ️ Base image copy path {base_copied_path} does not exist.")
        print("Ensure 'models/rf_models' exists in your project root when building the Modal app.")
        return False

    # Destination directory in the Modal Volume
    volume_dest_dir = Path("/app/storage/models/rf_models")
    volume_dest_dir.mkdir(parents=True, exist_ok=True)

    try:
        binary_dir_volume = volume_dest_dir / "model_binary"
        multiclass_dir_volume = volume_dest_dir / "model_multiclass"
        binary_dir_volume.mkdir(parents=True, exist_ok=True)
        multiclass_dir_volume.mkdir(parents=True, exist_ok=True)

        uploaded_files_log = []
        files_to_copy = [
            ("model_binary/rf_model_binary.joblib", binary_dir_volume / "rf_model_binary.joblib"),
            ("model_binary/standard_scaler.joblib", binary_dir_volume / "standard_scaler.joblib"),
            ("model_multiclass/rf_model_multiclass.joblib", multiclass_dir_volume / "rf_model_multiclass.joblib"),
            ("model_multiclass/standard_scaler.joblib", multiclass_dir_volume / "standard_scaler.joblib"),
        ]

        for rel_src_path, abs_dest_path in files_to_copy:
            src_file_in_image = image_source_dir / rel_src_path
            if src_file_in_image.exists():
                shutil.copy2(src_file_in_image, abs_dest_path)
                uploaded_files_log.append(str(rel_src_path))
                print(f"✅ Copied from image to volume: {rel_src_path}")
            else:
                print(f"⚠️ Source file not found in image: {src_file_in_image}. Skipping.")

        binary_features = [
            'seq', 'offset', 'sttl', 'ackdat', 'tcprtt', 'smeanpktsz',
            'shops', 'dttl', 'srcbytes', 'totbytes', 'dmeanpktsz', 'srcwin', 'stos'
        ]
        multiclass_features = [
            'ackdat', 'shops', 'seq', 'tcprtt', 'dmeanpktsz', 'offset', 'sttl',
            'srctcpbase', 'smeanpktsz', 'dstloss', 'loss', 'dttl', 'srcbytes', 'totbytes'
        ]
        class_labels = {
            "0": 'Benign', "1": 'UDPFlood', "2": 'HTTPFlood', "3": 'SlowrateDoS',
            "4": 'TCPConnectScan', "5": 'SYNScan', "6": 'UDPScan',
            "7": 'SYNFlood', "8": 'ICMPFlood'
        }
        feature_info = {
            "binary_features": binary_features,
            "multiclass_features": multiclass_features,
            "class_labels": class_labels
        }

        feature_info_path_volume = volume_dest_dir / "feature_info.json"
        with open(feature_info_path_volume, 'w') as f:
            json.dump(feature_info, f, indent=2)
        uploaded_files_log.append("feature_info.json")
        print(f"✅ Created feature_info.json in volume")

        volume.commit()
        print(f"🎉 Successfully processed {len(uploaded_files_log)} files for upload to Modal volume.")
        return True

    except Exception as e:
        print(f"❌ Error during model copy to volume: {e}", file=sys.stderr)
        return False

@app.function(
    image=image, # Needs joblib for the test load
    volumes={"/app/storage": volume},
    timeout=300
)
def verify_models_in_volume() -> bool: # Renamed for clarity
    """Verify models are properly uploaded to volume"""
    print("🔍 Verifying uploaded models in volume...")
    volume_models_dir = Path("/app/storage/models/rf_models")
    expected_files = [
        "model_binary/rf_model_binary.joblib",
        "model_binary/standard_scaler.joblib",
        "model_multiclass/rf_model_multiclass.joblib",
        "model_multiclass/standard_scaler.joblib",
        "feature_info.json"
    ]
    all_found = True
    for file_path_str in expected_files:
        full_path_in_volume = volume_models_dir / file_path_str
        if full_path_in_volume.exists():
            size = full_path_in_volume.stat().st_size
            print(f"✅ Found in volume: {file_path_str} ({size:,} bytes)")
        else:
            print(f"❌ Missing in volume: {file_path_str}")
            all_found = False
    if all_found:
        print("🎉 All model files verified successfully in volume!")
        try:
            import joblib
            test_model_path = volume_models_dir / "model_binary" / "rf_model_binary.joblib"
            if test_model_path.exists():
                test_model = joblib.load(test_model_path)
                print(f"✅ Test model load from volume successful. Model type: {type(test_model).__name__}")
            else:
                print(f"⚠️ Test model file not found at {test_model_path}, skipping load test.")
        except Exception as e:
            print(f"⚠️ Model load test from volume failed: {e}", file=sys.stderr)
    return all_found

@app.local_entrypoint()
def main():
    print("🛡️ 5G Security Analytics - Modal Model Setup (Image Copy Method)")
    print("=" * 60)
    
    # Call the renamed function
    upload_success = upload_models_to_volume.remote()
    
    if upload_success:
        print("\\n🔍 Verifying upload in volume...")
        # Call the renamed function
        verify_success = verify_models_in_volume.remote()
        
        if verify_success:
            print("\\n✅ Model setup completed successfully!")
            print("You can now deploy your Modal backend with: modal deploy modal_backend.py")
        else:
            print("\\n❌ Model verification in volume failed.")
    else:
        print("\\n❌ Model upload to volume failed.")

if __name__ == "__main__":
    main()
