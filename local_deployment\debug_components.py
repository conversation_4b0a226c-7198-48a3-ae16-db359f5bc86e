#!/usr/bin/env python3
"""
Simple test for local deployment components
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
parent_dir = os.path.abspath('..')
sys.path.insert(0, parent_dir)
print(f"Added to Python path: {parent_dir}")

def test_imports():
    """Test importing modules"""
    print("\n=== Testing Imports ===")
    
    try:
        from src.core.data_loader import DataLoader
        print("✅ DataLoader imported successfully")
    except Exception as e:
        print(f"❌ DataLoader import failed: {e}")
        return False
        
    try:
        from src.models.anomaly_detector import AnomalyDetector
        print("✅ AnomalyDetector imported successfully")
    except Exception as e:
        print(f"❌ AnomalyDetector import failed: {e}")
        return False
        
    try:
        from src.core.batch_processor import BatchProcessor
        print("✅ BatchProcessor imported successfully")
    except Exception as e:
        print(f"❌ BatchProcessor import failed: {e}")
        return False
        
    return True

def test_data_loader():
    """Test DataLoader initialization"""
    print("\n=== Testing DataLoader ===")
    
    try:
        from src.core.data_loader import DataLoader
        
        # Check if data file exists
        data_path = Path("../data/sample_inputs/5g_nidd_dataset.csv")
        print(f"Data path: {data_path.absolute()}")
        print(f"Data exists: {data_path.exists()}")
        
        if not data_path.exists():
            print("❌ Data file not found")
            return False
            
        # Initialize DataLoader
        loader = DataLoader(str(data_path))
        print(f"✅ DataLoader initialized")
        print(f"✅ DataFrame loaded: {hasattr(loader, 'df') and loader.df is not None}")
        
        if hasattr(loader, 'df') and loader.df is not None:
            print(f"✅ Dataset shape: {loader.df.shape}")
            
        return True
        
    except Exception as e:
        print(f"❌ DataLoader test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_anomaly_detector():
    """Test AnomalyDetector initialization"""
    print("\n=== Testing AnomalyDetector ===")
    
    try:
        from src.models.anomaly_detector import AnomalyDetector
        
        # Check if models directory exists
        models_path = Path("../models/rf_models")
        print(f"Models path: {models_path.absolute()}")
        print(f"Models exists: {models_path.exists()}")
        
        if not models_path.exists():
            print("❌ Models directory not found")
            return False
            
        # Check individual model files
        binary_model = models_path / "model_binary" / "rf_model_binary.joblib"
        binary_scaler = models_path / "model_binary" / "standard_scaler.joblib"
        multi_model = models_path / "model_multiclass" / "rf_model_multiclass.joblib"
        multi_scaler = models_path / "model_multiclass" / "standard_scaler.joblib"
        
        print(f"Binary model exists: {binary_model.exists()}")
        print(f"Binary scaler exists: {binary_scaler.exists()}")
        print(f"Multi model exists: {multi_model.exists()}")
        print(f"Multi scaler exists: {multi_scaler.exists()}")
        
        # Initialize AnomalyDetector
        detector = AnomalyDetector(str(models_path))
        print(f"✅ AnomalyDetector initialized")
        print(f"✅ Binary model loaded: {hasattr(detector, 'binary_model') and detector.binary_model is not None}")
        print(f"✅ Multiclass model loaded: {hasattr(detector, 'multiclass_model') and detector.multiclass_model is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ AnomalyDetector test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processor():
    """Test BatchProcessor initialization"""
    print("\n=== Testing BatchProcessor ===")
    
    try:
        from src.models.anomaly_detector import AnomalyDetector
        from src.core.batch_processor import BatchProcessor
        
        # Initialize detector first
        models_path = Path("../models/rf_models")
        detector = AnomalyDetector(str(models_path))
        
        if not (hasattr(detector, 'binary_model') and detector.binary_model is not None):
            print("❌ Detector not properly initialized")
            return False
            
        # Initialize BatchProcessor
        processor = BatchProcessor(detector)
        print(f"✅ BatchProcessor initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ BatchProcessor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔧 Local Deployment Component Test")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed - cannot continue")
        return
        
    # Test DataLoader
    data_ok = test_data_loader()
    
    # Test AnomalyDetector
    detector_ok = test_anomaly_detector()
    
    # Test BatchProcessor
    processor_ok = test_batch_processor()
    
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"DataLoader: {'✅' if data_ok else '❌'}")
    print(f"AnomalyDetector: {'✅' if detector_ok else '❌'}")
    print(f"BatchProcessor: {'✅' if processor_ok else '❌'}")
    
    if data_ok and detector_ok and processor_ok:
        print("\n🎉 All components working correctly!")
        print("The local deployment should work now.")
    else:
        print("\n❌ Some components failed - check the errors above")

if __name__ == "__main__":
    main()
