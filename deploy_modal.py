#!/usr/bin/env python3
"""
Deployment script for 5G Security Analytics on Modal
Economical deployment for Hugging Face Agents-MCP-Hackathon Track 3
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def check_modal_installed():
    """Check if Modal is installed"""
    try:
        result = subprocess.run(["modal", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Modal CLI installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Modal CLI not found")
            return False
    except FileNotFoundError:
        print("❌ Modal CLI not found")
        return False

def install_modal():
    """Install Modal CLI"""
    print("📦 Installing Modal CLI...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "modal"], check=True)
        print("✅ Modal CLI installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install Modal CLI")
        return False

def setup_modal_auth():
    """Setup Modal authentication"""
    print("🔐 Setting up Modal authentication...")
    print("Please run: modal setup")
    print("This will open a browser window to authenticate with Modal")
    
    response = input("Have you completed Modal authentication? (y/n): ")
    return response.lower() == 'y'

def check_models_exist():
    """Check if models exist locally"""
    models_path = Path("models/rf_models")
    if not models_path.exists():
        print("❌ Models directory not found: models/rf_models")
        print("Please train your models first using the existing pipeline")
        return False
    
    required_files = [
        "binary_model.pkl",
        "multiclass_model.pkl",
        "binary_scaler.pkl", 
        "multiclass_scaler.pkl"
    ]
    
    missing_files = []
    for file in required_files:
        if not (models_path / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing model files: {missing_files}")
        return False
    
    print("✅ All required model files found")
    return True

def upload_models():
    """Upload models to Modal volume"""
    print("📤 Uploading models to Modal volume...")
    try:
        result = subprocess.run([
            "modal", "run", "setup_modal_models.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Models uploaded successfully")
            print(result.stdout)
            return True
        else:
            print("❌ Model upload failed")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error uploading models: {e}")
        return False

def deploy_backend():
    """Deploy Modal backend"""
    print("🚀 Deploying Modal backend...")
    try:
        result = subprocess.run([
            "modal", "deploy", "modal_backend.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Backend deployed successfully")
            print(result.stdout)
            
            # Extract URL from output
            lines = result.stdout.split('\n')
            for line in lines:
                if "https://" in line and "modal.run" in line:
                    url = line.strip()
                    print(f"🌐 Backend URL: {url}")
                    return url
            return True
        else:
            print("❌ Backend deployment failed")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error deploying backend: {e}")
        return False

def test_backend(backend_url):
    """Test deployed backend"""
    if not backend_url or not isinstance(backend_url, str):
        print("⚠️ No backend URL provided, skipping test")
        return True
    
    print("🧪 Testing deployed backend...")
    try:
        # Test health endpoint
        response = requests.get(f"{backend_url}/", timeout=30)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            
            # Test status endpoint
            status_response = requests.get(f"{backend_url}/status", timeout=30)
            if status_response.status_code == 200:
                status = status_response.json()
                print(f"✅ Backend status: Models loaded: {status.get('models_loaded', False)}")
                return True
            else:
                print("⚠️ Status endpoint failed")
                return False
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        return False

def update_frontend_config(backend_url):
    """Update frontend configuration with backend URL"""
    if not backend_url or not isinstance(backend_url, str):
        print("⚠️ No backend URL provided, skipping frontend config update")
        return
    
    print("⚙️ Updating frontend configuration...")
    
    # Update control panel configuration
    control_panel_path = Path("src/ui/control_panel.py")
    if control_panel_path.exists():
        try:
            content = control_panel_path.read_text()
            
            # Replace the backend URL
            old_url = 'MODAL_BACKEND_URL = "https://semskurto--5g-security-analytics-fastapi-app.modal.run"'
            new_url = f'MODAL_BACKEND_URL = "{backend_url}"'
            
            if old_url in content:
                content = content.replace(old_url, new_url)
                control_panel_path.write_text(content)
                print("✅ Control panel configuration updated")
            else:
                print("⚠️ Could not find backend URL in control panel config")
        except Exception as e:
            print(f"❌ Error updating control panel config: {e}")

def main():
    """Main deployment function"""
    print("🛡️ 5G Security Analytics - Modal Deployment")
    print("=" * 60)
    print("🏆 Hugging Face Agents-MCP-Hackathon Track 3")
    print("🚀 Deploying to Modal Serverless Platform")
    print("💰 Optimized for economical usage")
    print()
    
    # Step 1: Check Modal CLI
    if not check_modal_installed():
        if not install_modal():
            print("❌ Cannot proceed without Modal CLI")
            return
    
    # Step 2: Check authentication
    if not setup_modal_auth():
        print("❌ Modal authentication required")
        return
    
    # Step 3: Check models
    if not check_models_exist():
        print("❌ Please train your models first")
        print("Run: python demo.py or python test_system.py")
        return
    
    # Step 4: Upload models
    if not upload_models():
        print("❌ Model upload failed")
        return
    
    # Step 5: Deploy backend
    backend_url = deploy_backend()
    if not backend_url:
        print("❌ Backend deployment failed")
        return
    
    # Step 6: Test backend
    if not test_backend(backend_url):
        print("⚠️ Backend test failed, but deployment may still work")
    
    # Step 7: Update frontend config
    if isinstance(backend_url, str):
        update_frontend_config(backend_url)
    
    print("\n🎉 Deployment completed successfully!")
    print("=" * 60)
    print("✅ Modal backend is now running")
    if isinstance(backend_url, str):
        print(f"🌐 Backend URL: {backend_url}")
    print("🎛️ Start your local control panel: python app.py")
    print("📊 Start your local dashboard: streamlit run dashboard.py")
    print()
    print("💡 Your system now uses Modal serverless backend!")
    print("💰 You only pay for actual usage time")
    print("🏆 Perfect for Hugging Face Agents-MCP-Hackathon!")

if __name__ == "__main__":
    main()
