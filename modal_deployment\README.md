# 5G Security Analytics - MODAL DEPLOYMENT

## 🚀 Modal Serverless Deployment

Bu klasör **Modal platformunda** çalışan 5G Security Analytics sistemini içerir. Hem backend hem frontend Modal'da barındırılır.

### 📁 Dosyalar:
- `modal_full_app.py` - Tam <PERSON> uygulaması (Backend + Gradio UI)
- `deploy_full_modal.py` - Deployment script
- `README.md` - Bu dosya

### 🚀 Deployment:

#### G<PERSON><PERSON><PERSON><PERSON>:
```bash
pip install modal
modal setup  # Modal authentication
```

#### Deploy:
```bash
cd modal_deployment
python deploy_full_modal.py
```

Deployment script:
1. ✅ Modal CLI kontrolü
2. 🔐 Authentication kontrolü  
3. 📦 Model upload (volume'a)
4. 🚀 Full app deploy
5. 🧪 Test
6. 🌐 URL verir

### 🌐 Erişim:
Deploy sonrası URL örneği:
```
https://semskurto--5g-security-analytics-full-gradio-app.modal.run
```

### ✅ Avantajlar:
- 🚀 Serverless architecture
- 💰 Sadece kullanım zamanında ücret
- 📈 Otomatik scaling
- 🌍 Global erişim
- 🔧 Server yönetimi yok
- 🏆 Hackathon için ideal

### 🎛️ Özellikler:
- **Backend**: Modal functions
- **Frontend**: Gradio UI (Modal'da hosted)
- **Models**: Modal volume'da persistent
- **State**: Modal volume'da persistent
- **Processing**: Background Modal functions

### 📝 Notlar:
- Models `../models/rf_models/` klasöründen upload edilir
- Volume'lar: `5g-models`, `5g-state`
- App adı: `5g-security-analytics-full`

### 🐛 Debug:
```bash
modal logs 5g-security-analytics-full
modal app list
```
