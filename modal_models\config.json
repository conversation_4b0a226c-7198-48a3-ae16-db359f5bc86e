{"binary_features": ["seq", "offset", "sttl", "ack<PERSON>t", "tcprtt", "smeanpktsz", "shops", "dttl", "srcbytes", "totby<PERSON>", "dmeanpktsz", "srcwin", "stos"], "multiclass_features": ["ack<PERSON>t", "shops", "seq", "tcprtt", "dmeanpktsz", "offset", "sttl", "srctcpbase", "smeanpktsz", "dstloss", "loss", "dttl", "srcbytes", "totby<PERSON>"], "class_labels": {"0": "Benign", "1": "UDPFlood", "2": "HTTPFlood", "3": "SlowrateDoS", "4": "TCPConnectScan", "5": "SYNScan", "6": "UDPScan", "7": "SYNFlood", "8": "ICMPFlood"}}