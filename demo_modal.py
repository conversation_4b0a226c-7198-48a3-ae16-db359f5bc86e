#!/usr/bin/env python3
"""
Demo script for Modal backend
Shows the power of serverless 5G security analytics
"""

import requests
import time
import json
import numpy as np
from typing import Dict, List, Any

# Modal backend URL (update after deployment)
BACKEND_URL = "https://semskurto--5g-security-analytics-fastapi-app.modal.run"

def generate_sample_data(count: int = 10) -> List[Dict[str, Any]]:
    """Generate sample 5G network data"""
    np.random.seed(42)
    
    sample_data = []
    for i in range(count):
        # Generate realistic 5G-NIDD dataset features
        record = {
            'Dur': np.random.exponential(1000),
            'Proto': np.random.choice([6, 17, 1]),  # TCP, UDP, ICMP
            'Dir': np.random.choice([0, 1]),
            'State': np.random.choice([0, 1, 2, 3]),
            'sTos': np.random.randint(0, 256),
            'dTos': np.random.randint(0, 256),
            'TotPkts': np.random.poisson(10),
            'TotBytes': np.random.exponential(5000),
            'SrcBytes': np.random.exponential(2500),
            'srcUdata': np.random.exponential(100),
            'dstUdata': np.random.exponential(100),
            'SrcLoad': np.random.exponential(1000),
            'DstLoad': np.random.exponential(1000),
            'SrcLoss': np.random.exponential(0.1),
            'DstLoss': np.random.exponential(0.1),
            'SrcJitter': np.random.exponential(10),
            'DstJitter': np.random.exponential(10),
            'sMaxPktSz': np.random.randint(64, 1500),
            'dMaxPktSz': np.random.randint(64, 1500)
        }
        
        # Add some anomalous patterns (20% of data for demo)
        if np.random.random() < 0.2:
            record['TotPkts'] = np.random.randint(100, 1000)  # High packet count
            record['TotBytes'] = np.random.exponential(50000)  # Large bytes
            record['Dur'] = np.random.exponential(10000)  # Long duration
        
        sample_data.append(record)
    
    return sample_data

def test_backend_health():
    """Test if Modal backend is healthy"""
    print("🔍 Testing Modal backend health...")
    try:
        response = requests.get(f"{BACKEND_URL}/", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Backend healthy: {result}")
            return True
        else:
            print(f"❌ Backend returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False

def test_backend_status():
    """Get backend status"""
    print("📊 Getting backend status...")
    try:
        response = requests.get(f"{BACKEND_URL}/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Backend status:")
            print(f"   Models loaded: {status.get('models_loaded', False)}")
            print(f"   Dataset available: {status.get('dataset_available', False)}")
            print(f"   Processing active: {status.get('processing_active', False)}")
            print(f"   Total processed: {status.get('total_processed', 0):,}")
            print(f"   Total anomalies: {status.get('total_anomalies', 0):,}")
            return True
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Status check error: {e}")
        return False

def test_single_detection():
    """Test single record detection"""
    print("🧪 Testing single record detection...")
    
    # Generate test data
    test_data = generate_sample_data(1)[0]
    print(f"📝 Test data sample: {list(test_data.keys())[:5]}...")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{BACKEND_URL}/detect",
            json={"data": test_data},
            timeout=30
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Detection successful:")
            print(f"   Anomaly: {'Yes' if result['is_anomaly'] else 'No'}")
            print(f"   Attack Type: {result['attack_type']}")
            print(f"   Confidence: {result['binary_confidence']:.3f}")
            print(f"   Processing Time: {result['processing_time_ms']:.1f}ms")
            print(f"   Total Request Time: {(end_time - start_time)*1000:.1f}ms")
            return True
        else:
            print(f"❌ Detection failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Detection error: {e}")
        return False

def test_batch_processing():
    """Test batch processing"""
    print("🔄 Testing batch processing...")
    
    batch_sizes = [10, 50, 100]
    
    for batch_size in batch_sizes:
        print(f"\n📦 Testing batch size: {batch_size}")
        
        # Generate batch data
        batch_data = generate_sample_data(batch_size)
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{BACKEND_URL}/detect/batch",
                json={"batch_data": batch_data, "batch_size": batch_size},
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                total_time = end_time - start_time
                
                print(f"✅ Batch processing successful:")
                print(f"   Records processed: {result['total_processed']}")
                print(f"   Anomalies detected: {result['total_anomalies']}")
                print(f"   Anomaly rate: {(result['total_anomalies']/result['total_processed']*100):.1f}%")
                print(f"   Backend processing: {result['processing_time_ms']:.1f}ms")
                print(f"   Total request time: {total_time*1000:.1f}ms")
                print(f"   Throughput: {result['total_processed']/total_time:.1f} records/sec")
            else:
                print(f"❌ Batch processing failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Batch processing error: {e}")
            return False
    
    return True

def demo_real_time_processing():
    """Demo real-time processing simulation"""
    print("⚡ Demo: Real-time processing simulation...")
    print("Processing 5 batches with 1-second intervals...")
    
    total_processed = 0
    total_anomalies = 0
    total_time = 0
    
    for i in range(5):
        print(f"\n🔄 Batch {i+1}/5")
        
        # Generate batch
        batch_data = generate_sample_data(20)
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{BACKEND_URL}/detect/batch",
                json={"batch_data": batch_data},
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                batch_time = end_time - start_time
                
                total_processed += result['total_processed']
                total_anomalies += result['total_anomalies']
                total_time += batch_time
                
                print(f"   ✅ {result['total_processed']} records, {result['total_anomalies']} anomalies")
                print(f"   ⏱️ {batch_time*1000:.0f}ms ({result['total_processed']/batch_time:.1f} rec/sec)")
            else:
                print(f"   ❌ Batch failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Wait between batches
        if i < 4:
            time.sleep(1)
    
    print(f"\n📊 Real-time Demo Summary:")
    print(f"   Total processed: {total_processed:,} records")
    print(f"   Total anomalies: {total_anomalies:,}")
    print(f"   Overall anomaly rate: {(total_anomalies/max(total_processed,1)*100):.1f}%")
    print(f"   Average throughput: {total_processed/total_time:.1f} records/sec")
    print(f"   Total time: {total_time:.2f} seconds")

def main():
    """Main demo function"""
    print("🛡️ 5G Security Analytics - Modal Backend Demo")
    print("=" * 60)
    print("🏆 Hugging Face Agents-MCP-Hackathon Track 3")
    print("🚀 Serverless anomaly detection powered by Modal")
    print()
    
    # Test 1: Backend health
    if not test_backend_health():
        print("❌ Backend not accessible. Please deploy first:")
        print("   python deploy_modal.py")
        return
    
    print()
    
    # Test 2: Backend status
    if not test_backend_status():
        print("⚠️ Status check failed, but continuing...")
    
    print()
    
    # Test 3: Single detection
    if not test_single_detection():
        print("❌ Single detection failed")
        return
    
    print()
    
    # Test 4: Batch processing
    if not test_batch_processing():
        print("❌ Batch processing failed")
        return
    
    print()
    
    # Test 5: Real-time simulation
    demo_real_time_processing()
    
    print("\n🎉 Demo completed successfully!")
    print("=" * 60)
    print("✅ Modal backend is working perfectly")
    print("💰 Economical serverless processing")
    print("⚡ Real-time anomaly detection")
    print("🏆 Ready for hackathon submission!")
    print()
    print("🎛️ Start control panel: python app.py")
    print("📊 Start dashboard: streamlit run dashboard.py")

if __name__ == "__main__":
    main()
