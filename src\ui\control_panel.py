"""
Gradio Control Panel for 5G Security Analytics
"""

import gradio as gr
import json
import os
import sys
import time
import threading
import logging
import requests
from pathlib import Path
from typing import Dict, Any, Tuple, List

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.core.data_loader import DataLoader
from src.core.batch_processor import BatchProcessor
from src.models.anomaly_detector import AnomalyDetector

# Modal Backend Configuration
MODAL_BACKEND_URL = "https://semskurto--5g-security-analytics-fastapi-app.modal.run"
USE_MODAL_BACKEND = True  # Set to False to use local backend

logger = logging.getLogger(__name__)

# Shared state file
STATE_FILE = "processing_state.json"

def load_state():
    """Load processing state from file"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r') as f:
                return json.load(f)
        except:
            pass
    return {
        "is_processing": False,
        "batch_size": 100,
        "max_batches": 10,
        "requests_per_second": 10.0,
        "processing_mode": "Manual Batch",
        "current_results": [],
        "processing_stats": {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        },
        "log_messages": []
    }

def save_state(state):
    """Save processing state to file"""
    try:
        with open(STATE_FILE, 'w') as f:
            json.dump(state, f)
    except Exception as e:
        logger.error(f"Error saving state: {e}")

def add_log(message: str, level: str = "INFO"):
    """Add log message to state"""
    state = load_state()
    timestamp = time.strftime("%H:%M:%S")
    log_entry = f"[{timestamp}] {level}: {message}"
    state["log_messages"].append(log_entry)
    
    # Keep only last 100 messages
    if len(state["log_messages"]) > 100:
        state["log_messages"] = state["log_messages"][-100:]
    
    save_state(state)
    logger.info(message)

class ControlPanel:
    """
    Gradio control panel for managing processing
    Supports both local and Modal backend
    """

    def __init__(self):
        """Initialize control panel"""
        self.data_loader = None
        self.detector = None
        self.batch_processor = None
        self.processing_thread = None
        self.use_modal = USE_MODAL_BACKEND

        # Initialize components
        self.initialize_components()
    
    def initialize_components(self):
        """Initialize data loader, detector, and batch processor"""
        try:
            if self.use_modal:
                # Modal backend mode
                add_log("Using Modal backend for processing")
                self._check_modal_backend()

                # Still need local data loader for generating test data
                if os.path.exists("data/sample_inputs/5g_nidd_dataset.csv"):
                    self.data_loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
                    add_log("Local data loader initialized for test data generation")
                else:
                    add_log("Local dataset not found - will use generated data", "WARNING")
            else:
                # Local backend mode
                add_log("Using local backend for processing")

                # Initialize data loader
                if os.path.exists("data/sample_inputs/5g_nidd_dataset.csv"):
                    self.data_loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
                    add_log("Data loader initialized successfully")
                else:
                    add_log("Dataset not found", "ERROR")

                # Initialize detector
                if os.path.exists("models/rf_models"):
                    self.detector = AnomalyDetector("models/rf_models")
                    if self.detector.binary_model is not None:
                        self.batch_processor = BatchProcessor(self.detector)
                        add_log("Models loaded successfully")
                    else:
                        add_log("Models failed to load", "ERROR")
                else:
                    add_log("Models directory not found", "ERROR")

        except Exception as e:
            add_log(f"Error initializing components: {e}", "ERROR")

    def _check_modal_backend(self):
        """Check if Modal backend is accessible"""
        try:
            response = requests.get(f"{MODAL_BACKEND_URL}/", timeout=10)
            if response.status_code == 200:
                add_log("✅ Modal backend is accessible")

                # Get backend status
                status_response = requests.get(f"{MODAL_BACKEND_URL}/status", timeout=10)
                if status_response.status_code == 200:
                    status = status_response.json()
                    add_log(f"Modal backend status: Models loaded: {status.get('models_loaded', False)}")
                else:
                    add_log("Could not get Modal backend status", "WARNING")
            else:
                add_log(f"Modal backend returned status {response.status_code}", "ERROR")
                self.use_modal = False
        except Exception as e:
            add_log(f"Cannot connect to Modal backend: {e}", "ERROR")
            add_log("Falling back to local backend", "WARNING")
            self.use_modal = False
    
    def get_system_status(self) -> Tuple[str, str, str]:
        """Get current system status"""
        if self.use_modal:
            # Modal backend status
            try:
                response = requests.get(f"{MODAL_BACKEND_URL}/status", timeout=10)
                if response.status_code == 200:
                    status = response.json()
                    dataset_status = "✅ Modal backend connected"
                    model_status = f"✅ Modal models: {'Loaded' if status.get('models_loaded') else 'Not loaded'}"
                else:
                    dataset_status = "❌ Modal backend error"
                    model_status = "❌ Cannot check models"
            except:
                dataset_status = "❌ Modal backend offline"
                model_status = "❌ Cannot connect to Modal"
        else:
            # Local backend status
            if self.data_loader and self.data_loader.df is not None:
                dataset_info = self.data_loader.get_dataset_info()
                dataset_status = f"✅ Dataset loaded: {dataset_info['total_records']:,} records"
            else:
                dataset_status = "❌ Dataset not loaded"

            if self.detector and self.detector.binary_model is not None:
                model_status = "✅ Local models loaded"
            else:
                model_status = "❌ Local models not loaded"

        # Processing status
        state = load_state()
        backend_type = "Modal" if self.use_modal else "Local"
        if state["is_processing"]:
            processing_status = f"🔄 Processing ({backend_type})... Batch {state['processing_stats']['current_batch']}"
        else:
            processing_status = f"⏸️ Ready ({backend_type})"

        return dataset_status, model_status, processing_status
    
    def start_processing(self, batch_size: int, max_batches: int,
                        requests_per_second: float, processing_mode: str) -> Tuple[str, str, str, str]:
        """Start batch processing"""
        if self.use_modal:
            # Check Modal backend availability
            try:
                response = requests.get(f"{MODAL_BACKEND_URL}/", timeout=10)
                if response.status_code != 200:
                    add_log("Modal backend not accessible", "ERROR")
                    return self.get_system_status() + ("❌ Modal backend not accessible",)
            except:
                add_log("Cannot connect to Modal backend", "ERROR")
                return self.get_system_status() + ("❌ Cannot connect to Modal backend",)
        else:
            # Check local components
            if not self.data_loader or not self.batch_processor:
                add_log("Cannot start processing: local components not initialized", "ERROR")
                return self.get_system_status() + ("❌ Cannot start: components not ready",)

        state = load_state()
        if state["is_processing"]:
            add_log("Processing already running", "WARNING")
            return self.get_system_status() + ("⚠️ Already processing",)

        # Reset data loader position if using local backend
        if not self.use_modal and self.data_loader:
            self.data_loader.reset_stream()

        # Clear previous results and update configuration
        state.update({
            "is_processing": True,
            "batch_size": batch_size,
            "max_batches": max_batches,
            "requests_per_second": requests_per_second,
            "processing_mode": processing_mode,
            "current_results": [],  # Clear previous results
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            }
        })
        save_state(state)

        backend_type = "Modal" if self.use_modal else "Local"
        add_log(f"Starting {backend_type} processing: {batch_size} records/batch, {max_batches} batches max")

        # Start processing in background thread
        self.processing_thread = threading.Thread(
            target=self._process_batches,
            args=(batch_size, max_batches, requests_per_second),
            daemon=True
        )
        self.processing_thread.start()

        add_log(f"{backend_type} processing thread started successfully")
        return self.get_system_status() + (f"🚀 {backend_type} processing started",)
    
    def stop_processing(self) -> Tuple[str, str, str, str]:
        """Stop processing"""
        state = load_state()
        state["is_processing"] = False
        save_state(state)
        
        add_log("Processing stopped by user")
        return self.get_system_status() + ("⏹️ Processing stopped",)
    
    def _process_batches(self, batch_size: int, max_batches: int, requests_per_second: float):
        """Process batches in background thread"""
        try:
            inter_record_delay = 1.0 / requests_per_second if requests_per_second > 0 else 0.01

            for batch_num in range(max_batches):
                state = load_state()
                if not state["is_processing"]:
                    break

                # Get batch data from real 5G-NIDD dataset
                if self.use_modal:
                    # For Modal backend, send request to process real dataset
                    try:
                        response = requests.post(f"{MODAL_BACKEND_URL}/process_real_batch",
                                               json={"batch_size": batch_size}, timeout=30)
                        if response.status_code == 200:
                            batch_results = response.json().get("results", [])
                        else:
                            add_log("Modal backend processing failed", "ERROR")
                            break
                    except Exception as e:
                        add_log(f"Modal backend error: {e}", "ERROR")
                        break
                else:
                    # Local backend uses real dataset
                    batch_df = self.data_loader.get_batch(batch_size)
                    if batch_df is None or batch_df.empty:
                        add_log("No more data to process from 5G-NIDD dataset")
                        break
                    batch_data = batch_df.to_dict('records')

                # Update current batch
                state["processing_stats"]["current_batch"] = batch_num + 1
                save_state(state)

                # Process batch
                start_time = time.time()

                if self.use_modal:
                    batch_results = self._process_batch_modal(batch_data)
                else:
                    batch_results = self.batch_processor.process_batch_sequential(batch_data)

                processing_time = time.time() - start_time

                # Update results and stats
                state["current_results"].extend(batch_results)

                # Keep only last 1000 results for memory efficiency
                if len(state["current_results"]) > 1000:
                    state["current_results"] = state["current_results"][-1000:]

                anomaly_count = sum(1 for r in batch_results if r.get("is_anomaly", False))

                state["processing_stats"]["total_processed"] += len(batch_results)
                state["processing_stats"]["total_anomalies"] += anomaly_count
                state["processing_stats"]["processing_rate"] = len(batch_results) / processing_time if processing_time > 0 else 0

                save_state(state)

                backend_type = "Modal" if self.use_modal else "Local"
                add_log(f"Batch {batch_num + 1} completed ({backend_type}): {len(batch_results)} records, {anomaly_count} anomalies")

                # Simulate delay between batches
                time.sleep(inter_record_delay * len(batch_results))

            # Mark processing as complete
            state = load_state()
            state["is_processing"] = False
            save_state(state)

            add_log(f"Processing completed: {state['processing_stats']['total_processed']} total records")

        except Exception as e:
            add_log(f"Error during processing: {e}", "ERROR")
            state = load_state()
            state["is_processing"] = False
            save_state(state)

    def _generate_sample_batch(self, batch_size: int) -> List[Dict[str, Any]]:
        """Generate sample 5G network data for Modal backend"""
        import numpy as np

        np.random.seed(int(time.time()) % 1000)  # Different seed each time

        sample_data = []
        for i in range(batch_size):
            # Generate realistic 5G-NIDD dataset features
            record = {
                'Dur': np.random.exponential(1000),
                'Proto': np.random.choice([6, 17, 1]),  # TCP, UDP, ICMP
                'Dir': np.random.choice([0, 1]),
                'State': np.random.choice([0, 1, 2, 3]),
                'sTos': np.random.randint(0, 256),
                'dTos': np.random.randint(0, 256),
                'TotPkts': np.random.poisson(10),
                'TotBytes': np.random.exponential(5000),
                'SrcBytes': np.random.exponential(2500),
                'srcUdata': np.random.exponential(100),
                'dstUdata': np.random.exponential(100),
                'SrcLoad': np.random.exponential(1000),
                'DstLoad': np.random.exponential(1000),
                'SrcLoss': np.random.exponential(0.1),
                'DstLoss': np.random.exponential(0.1),
                'SrcJitter': np.random.exponential(10),
                'DstJitter': np.random.exponential(10),
                'sMaxPktSz': np.random.randint(64, 1500),
                'dMaxPktSz': np.random.randint(64, 1500)
            }

            # Add some anomalous patterns (10% of data)
            if np.random.random() < 0.1:
                record['TotPkts'] = np.random.randint(100, 1000)  # High packet count
                record['TotBytes'] = np.random.exponential(50000)  # Large bytes
                record['Dur'] = np.random.exponential(10000)  # Long duration

            sample_data.append(record)

        return sample_data

    def _process_batch_modal(self, batch_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process batch using Modal backend"""
        try:
            response = requests.post(
                f"{MODAL_BACKEND_URL}/detect/batch",
                json={"batch_data": batch_data},
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                return result["results"]
            else:
                add_log(f"Modal batch processing failed: {response.status_code}", "ERROR")
                return []

        except Exception as e:
            add_log(f"Error calling Modal backend: {e}", "ERROR")
            return []
    
    def clear_results(self) -> Tuple[str, str, str, str]:
        """Clear all results and reset stats"""
        state = load_state()
        state.update({
            "current_results": [],
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            },
            "log_messages": []
        })
        save_state(state)
        
        add_log("Results and statistics cleared")
        return self.get_system_status() + ("🧹 Results cleared",)
    
    def get_current_stats(self) -> str:
        """Get current processing statistics"""
        state = load_state()
        stats = state.get("processing_stats", {})
        
        current_state = load_state()
        is_processing = current_state.get('is_processing', False)

        return f"""Current Statistics:
• Total Processed: {stats.get('total_processed', 0):,} records
• Total Anomalies: {stats.get('total_anomalies', 0):,}
• Current Batch: {stats.get('current_batch', 0)}
• Processing Rate: {stats.get('processing_rate', 0):.1f} records/sec
• Anomaly Rate: {(stats.get('total_anomalies', 0) / max(stats.get('total_processed', 1), 1) * 100):.1f}%
• Status: {'🔄 Processing' if is_processing else '⏸️ Ready'}

Current Configuration:
• Batch Size: {current_state.get('batch_size', 100)}
• Max Batches: {current_state.get('max_batches', 10)}
• Requests/Second: {current_state.get('requests_per_second', 10.0)}
• Processing Mode: {current_state.get('processing_mode', 'Manual Batch')}"""


def create_control_panel() -> gr.Blocks:
    """Create the Gradio control panel interface"""

    control_panel = ControlPanel()

    # Clean, minimal CSS
    custom_css = """
    .gradio-container {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    """

    with gr.Blocks(
        title="5G Security Analytics - Professional Control Panel",
        css=custom_css
    ) as demo:
        # Header with backend indicator
        backend_type = "Modal Serverless" if USE_MODAL_BACKEND else "Local"
        gr.Markdown(f"# 5G Security Analytics - Control Panel ({backend_type})")
        gr.Markdown("**Real-time Network Threat Detection using Authentic 5G-NIDD Dataset**")
        if USE_MODAL_BACKEND:
            gr.Markdown("🚀 **Powered by Modal Serverless Platform** - Hugging Face Agents-MCP-Hackathon Track 3")
        gr.Markdown("📊 **Processing 415,891 real 5G network records** - Configure and monitor the security analytics system")

        # System Status
        gr.Markdown("## System Status")

        with gr.Row():
            with gr.Column():
                dataset_status = gr.Textbox(
                    label="Dataset Status",
                    interactive=False
                )
                model_status = gr.Textbox(
                    label="Model Status",
                    interactive=False
                )
            with gr.Column():
                processing_status = gr.Textbox(
                    label="Processing Status",
                    interactive=False
                )
                operation_status = gr.Textbox(
                    label="Last Operation",
                    interactive=False
                )

        # Configuration
        gr.Markdown("## Configuration")

        with gr.Row():
            with gr.Column():
                batch_size = gr.Slider(
                    minimum=10, maximum=1000, value=100, step=10,
                    label="Batch Size",
                    info="Number of real 5G-NIDD records to process per batch (415,891 total available)"
                )
                max_batches = gr.Slider(
                    minimum=1, maximum=100, value=100, step=1,
                    label="Maximum Batches",
                    info="Maximum number of batches to process from real 5G-NIDD dataset"
                )

            with gr.Column():
                requests_per_second = gr.Slider(
                    minimum=0.1, maximum=100.0, value=10.0, step=0.1,
                    label="Requests per Second",
                    info="Real 5G-NIDD dataset processing rate (records per second)"
                )
                processing_mode = gr.Dropdown(
                    choices=["Manual Batch", "Continuous Stream"],
                    value="Manual Batch",
                    label="Processing Mode",
                    info="Select processing mode"
                )

        # Controls
        gr.Markdown("## Controls")

        with gr.Row():
            start_btn = gr.Button("Start Processing", variant="primary")
            stop_btn = gr.Button("Stop Processing", variant="stop")
            clear_btn = gr.Button("Clear Results", variant="secondary")
            refresh_btn = gr.Button("Refresh Status")

        # Statistics
        gr.Markdown("## Statistics")

        stats_display = gr.Textbox(
            label="Processing Statistics",
            lines=8,
            interactive=False
        )

        # Information
        with gr.Accordion("User Guide", open=False):
            gr.Markdown("""
            ### Quick Start
            1. Check system status to ensure 5G-NIDD dataset and models are loaded
            2. Configure batch size, processing rate, and mode
            3. Click "Start Processing" to begin real dataset analysis
            4. Monitor progress in the Streamlit dashboard
            5. Use controls to stop, clear, or refresh as needed

            ### Configuration
            - **Batch Size**: Number of real 5G-NIDD records processed together (10-1000)
            - **Max Batches**: Maximum number of batches from 415,891 total records (1-100)
            - **Requests/Second**: Real dataset processing speed control (0.1-100)
            - **Processing Mode**: Manual batch or continuous stream from authentic dataset

            ### Dashboard
            Open Streamlit dashboard for real-time monitoring:
            ```
            streamlit run dashboard.py
            ```
            """)

        # Event Handlers
        def refresh_status():
            """Refresh all status displays"""
            ds_status, m_status, p_status = control_panel.get_system_status()
            stats = control_panel.get_current_stats()
            return ds_status, m_status, p_status, "Status refreshed", stats

        def start_processing_handler(batch_size_val, max_batches_val, rps_val, mode_val):
            """Handle start processing"""
            return control_panel.start_processing(
                int(batch_size_val), int(max_batches_val), float(rps_val), mode_val
            ) + (control_panel.get_current_stats(),)

        def stop_processing_handler():
            """Handle stop processing"""
            return control_panel.stop_processing() + (control_panel.get_current_stats(),)

        def clear_results_handler():
            """Handle clear results"""
            return control_panel.clear_results() + (control_panel.get_current_stats(),)

        # Wire up event handlers
        start_btn.click(
            fn=start_processing_handler,
            inputs=[batch_size, max_batches, requests_per_second, processing_mode],
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        stop_btn.click(
            fn=stop_processing_handler,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        clear_btn.click(
            fn=clear_results_handler,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        refresh_btn.click(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        # Auto-refresh on load
        demo.load(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        # Real-time auto-refresh using Gradio's built-in timer
        def auto_refresh():
            """Auto refresh function"""
            return refresh_status()

        # Set up auto-refresh timer (every 2 seconds)
        timer = gr.Timer(2.0)
        timer.tick(
            fn=auto_refresh,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

    return demo


if __name__ == "__main__":
    demo = create_control_panel()
    demo.launch(server_name="127.0.0.1", server_port=7860, share=False)
