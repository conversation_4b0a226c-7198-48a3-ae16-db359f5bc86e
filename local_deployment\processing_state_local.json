{"is_processing": false, "batch_size": 100, "max_batches": 10, "requests_per_second": 10.0, "processing_mode": "Manual Batch", "current_results": [], "processing_stats": {"total_processed": 0, "total_anomalies": 0, "current_batch": 0, "processing_rate": 0.0}, "log_messages": ["[09:52:39] INFO: Using LOCAL backend for processing", "[09:52:39] INFO: Project root: C:\\Users\\<USER>\\Documents\\hg-hackathon25-track3", "[09:52:39] INFO: Looking for dataset at: C:\\Users\\<USER>\\Documents\\hg-hackathon25-track3\\data\\sample_inputs\\5g_nidd_dataset.csv", "[09:52:39] INFO: Looking for models at: C:\\Users\\<USER>\\Documents\\hg-hackathon25-track3\\models\\rf_models", "[09:52:41] INFO: Data loader initialized successfully", "[09:52:43] INFO: Models loaded successfully"]}